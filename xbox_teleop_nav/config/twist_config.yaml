twist_to_pose_node:
  ros__parameters:
    frame_id: "tool"
    child_frame: "base_link"
    sub_twist_topic: "/mofo_teleop/xbox/target_twist"
    pub_pose_topic: "/mofo_teleop/target_ee_poses"                    # 发布位姿的话题
    publish_frequency: 250.0  # Hz for publishing pose
    max_linear: [0.1, 0.1, 0.1]   # 最大线速度 m/s (x, y, z)
    max_angular: [0.1, 0.1, 0.1]  # 最大角速度 rad/s (x, y, z)
    # 6D空间限位保护
    pos_min: [-1.0, -1.0, -1.0]
    pos_max: [1.0, 1.0, 1.0]
    euler_min: [-3.14, -3.14, -3.14]
    euler_max: [3.14, 3.14, 3.14]
    # 原点姿态映射
    origin_position: [0.0, 0.0, 0.0]
    origin_euler: [0.0, 0.0, -90.0]  # origin roll, pitch, yaw in degrees
    # 初始位姿配置（reset时重置到此位姿）
    initial_position: [0.0, 0.0, 0.0]  # 初始位置 m (x, y, z)
    initial_euler: [0.0, 0.0, 0.0]     # 初始姿态 degrees (roll, pitch, yaw)


