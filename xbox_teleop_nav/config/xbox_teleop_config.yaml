# Xbox Teleop Navigation 统一配置文件
# 6DOF控制：3D位置 + 3D姿态

teleop_node:
  ros__parameters:
    # 发布频率 (Hz) - 轨迹规划器dt = 1/publish_rate
    publish_rate: 100
    
    # 手柄轴映射 [x, y, z]
    axes:
      linear: [1, 0, 4]      # 线速度轴 (左摇杆X, 左摇杆Y, 右扳机)
      angular: [2, 2, 3]     # 角速度轴 (右摇杆X, 右摇杆X, 右摇杆Y)
      jog: [7, 6]            # 微调轴 (十字键)
    
    # 死区配置 [x, y, z]
    deadzone:
      linear: [0.05, 0.05, 0.05]
      angular: [0.05, 0.05, 0.05]
    
    # 方向配置 [x, y, z] (1.0 或 -1.0)
    direction:
      linear: [1.0, 1.0, 1.0]
      angular: [1.0, 1.0, 1.0]
    
    # 缩放因子 [x, y, z]
    scale:
      linear: [1.0, 1.0, 1.0]
      angular: [0.0, 0.0, 1.0]
      jog: [0.05, 0.05]
    
    # 按钮映射
    buttons:
      start: 7
      reset: 6
      sucker: 0
      record: 3

twist_to_pose_node:
  ros__parameters:
    # 话题配置
    topics:
      twist_input: "/mofo_teleop/xbox/target_twist"
      pose_output: "/mofo_teleop/target_ee_poses"
    
    # 坐标系配置
    frames:
      parent: "tool"
      child: "base_link"
    
    # 发布频率
    publish_frequency: 100.0
    
    # 6D速度限制 [linear_x, linear_y, linear_z, angular_x, angular_y, angular_z]
    velocity_limits:
      linear: [0.1, 0.1, 0.1]    # m/s
      angular: [0.1, 0.1, 0.1]   # rad/s
    
    # 6D位姿限制
    pose_limits:
      position:
        min: [-1.0, -1.0, -1.0]  # m
        max: [1.0, 1.0, 1.0]     # m
      orientation:
        min: [-3.14, -3.14, -3.14]  # rad (roll, pitch, yaw)
        max: [3.14, 3.14, 3.14]     # rad
    
    # 6D初始位姿 (reset时重置到此位姿)
    initial_pose:
      position: [0.0, 0.0, 0.0]     # m [x, y, z]
      orientation: [0.0, 0.0, 0.0]  # degrees [roll, pitch, yaw]
    
    # 轨迹规划参数 (dt自动计算为1/publish_rate)
    trajectory:
      max_velocity: [0.5, 0.5, 0.3, 1.0, 1.0, 1.0]        # [lin_x, lin_y, lin_z, ang_x, ang_y, ang_z]
      max_acceleration: [2.0, 2.0, 1.5, 3.0, 3.0, 3.0]    # 最大加速度
      max_jerk: [10.0, 10.0, 8.0, 15.0, 15.0, 15.0]       # 最大jerk
      history_length: 5                                     # 速度历史长度
