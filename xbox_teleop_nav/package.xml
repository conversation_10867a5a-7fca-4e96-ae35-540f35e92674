<?xml version="1.0"?>
<package format="3">
  <name>xbox_teleop_nav</name>
  <version>0.1.0</version>
  <description>Xbox teleop and twist to pose converter</description>
  <maintainer email="<EMAIL>">autogen</maintainer>
  <license>MIT</license>
  
  <buildtool_depend>ament_python</buildtool_depend>
  <buildtool_depend>ament_cmake</buildtool_depend>

  <build_depend>rosidl_default_generators</build_depend>
  <exec_depend>rosidl_default_runtime</exec_depend>

  <exec_depend>rclpy</exec_depend>
  <exec_depend>sensor_msgs</exec_depend>
  <exec_depend>geometry_msgs</exec_depend>
  <exec_depend>std_msgs</exec_depend>
  <exec_depend>tf2_ros</exec_depend>
  <exec_depend>scipy</exec_depend>
  <exec_depend>numpy</exec_depend>

  <export>
    <build_type>ament_python</build_type>
  </export>
</package>
