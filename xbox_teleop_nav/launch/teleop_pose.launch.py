from launch import LaunchDescription
from launch_ros.actions import Node
from ament_index_python.packages import get_package_share_directory
import os

def generate_launch_description():
    pkg_share = get_package_share_directory('xbox_teleop_nav')
    joystick_config = os.path.join(pkg_share, 'config', 'joystick_config.yaml')
    twist_config = os.path.join(pkg_share, 'config', 'twist_config.yaml')
    return LaunchDescription([
        Node(
            package='joy',
            executable='joy_node',
            name='joy_node'
        ),
        Node(
            package='xbox_teleop_nav',
            executable='teleop_node',
            name='teleop_node',
            parameters=[joystick_config],
            output='screen'
        ),
        Node(
            package='xbox_teleop_nav',
            executable='twist_to_pose_node',
            name='twist_to_pose_node',
            parameters=[twist_config],
            output='screen'
        )
    ])
