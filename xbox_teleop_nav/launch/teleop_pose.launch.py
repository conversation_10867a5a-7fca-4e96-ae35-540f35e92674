from launch import LaunchDescription
from launch_ros.actions import Node
from ament_index_python.packages import get_package_share_directory
import os

def generate_launch_description():
    # 统一配置文件
    config_file = os.path.join(
        get_package_share_directory('xbox_teleop_nav'),
        'config',
        'xbox_teleop_config.yaml'
    )

    return LaunchDescription([
        Node(
            package='joy',
            executable='joy_node',
            name='joy_node',
            parameters=[{
                'device_id': 0,
                'deadzone': 0.05,
                'autorepeat_rate': 20.0,
            }]
        ),
        Node(
            package='xbox_teleop_nav',
            executable='teleop_node',
            name='teleop_node',
            parameters=[config_file],
            output='screen'
        ),
        Node(
            package='xbox_teleop_nav',
            executable='twist_to_pose_node',
            name='twist_to_pose_node',
            parameters=[config_file],
            output='screen'
        )
    ])
