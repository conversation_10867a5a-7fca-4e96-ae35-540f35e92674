from setuptools import setup

package_name = 'xbox_teleop_nav'

setup(
    name=package_name,
    version='0.1.0',
    packages=[package_name],
    data_files=[
        ('share/ament_index/resource_index/packages', ['resource/' + package_name]),
        ('share/' + package_name, ['package.xml']),
        ('share/' + package_name + '/launch', ['launch/teleop_pose.launch.py']),
        ('share/' + package_name + '/config', [
            'config/joystick_config.yaml',
            'config/twist_config.yaml'
        ])
        #,('share/' + package_name + '/msg', ['msg/Int32MultiArrayStamped.msg']),
    ],
    install_requires=['setuptools'],
    zip_safe=True,
    maintainer='autogen',
    maintainer_email='<EMAIL>',
    description='Xbox teleop and twist to pose conversion',
    license='MIT',
    entry_points={
        'console_scripts': [
            'teleop_node = xbox_teleop_nav.teleop_node:main',
            'twist_to_pose_node = xbox_teleop_nav.twist_to_pose_node:main',
        ],
    },
)
