#!/usr/bin/env python3
import rclpy
from rclpy.node import Node
from sensor_msgs.msg import Joy
from geometry_msgs.msg import TwistStamped
from std_msgs.msg import Int32
import numpy as np
from collections import deque


class TrajectoryPlanner:
    """6DOF S曲线轨迹规划器"""

    def __init__(self, dt, config=None):
        # 时间间隔参数
        self.dt = dt

        # 默认参数或从配置读取
        if config:
            self.max_velocity = np.array(config.get('max_velocity', [0.5, 0.5, 0.3, 1.0, 1.0, 1.0]))
            self.max_acceleration = np.array(config.get('max_acceleration', [2.0, 2.0, 1.5, 3.0, 3.0, 3.0]))
            self.max_jerk = np.array(config.get('max_jerk', [10.0, 10.0, 8.0, 15.0, 15.0, 15.0]))
            history_len = config.get('history_length', 5)
        else:
            self.max_velocity = np.array([0.5, 0.5, 0.3, 1.0, 1.0, 1.0])
            self.max_acceleration = np.array([2.0, 2.0, 1.5, 3.0, 3.0, 3.0])
            self.max_jerk = np.array([10.0, 10.0, 8.0, 15.0, 15.0, 15.0])
            history_len = 5

        # 状态变量
        self.current_velocity = np.zeros(6)
        self.current_acceleration = np.zeros(6)
        self.velocity_history = deque(maxlen=history_len)

    def plan_velocity_trajectory(self, target_vel_array):
        """S曲线速度轨迹规划 - 向量化优化版本"""
        target_vel = np.clip(target_vel_array, -self.max_velocity, self.max_velocity)
        vel_diff = target_vel - self.current_velocity
        max_accel_step = self.max_acceleration * self.dt
        max_jerk_step = self.max_jerk * self.dt * self.dt

        # 向量化处理
        small_diff_mask = np.abs(vel_diff) <= max_accel_step
        planned_velocity = np.where(small_diff_mask, target_vel, self.current_velocity)

        # 处理大差值情况
        large_diff_mask = ~small_diff_mask
        if np.any(large_diff_mask):
            accel_step = np.where(vel_diff > 0, max_accel_step, -max_accel_step)
            accel_diff = accel_step - self.current_acceleration * self.dt
            jerk_limited = np.where(np.abs(accel_diff) > max_jerk_step,
                                  self.current_acceleration * self.dt + np.sign(accel_diff) * max_jerk_step,
                                  accel_step)
            planned_velocity = np.where(large_diff_mask,
                                      self.current_velocity + jerk_limited,
                                      planned_velocity)
            self.current_acceleration = np.where(large_diff_mask, jerk_limited / self.dt, vel_diff / self.dt)

        self.current_velocity = planned_velocity
        self.velocity_history.append(planned_velocity.copy())

        return np.mean(self.velocity_history, axis=0) if len(self.velocity_history) > 1 else planned_velocity

    def reset(self):
        """重置轨迹规划器状态"""
        self.current_velocity.fill(0)
        self.current_acceleration.fill(0)
        self.velocity_history.clear()



class TeleopNode(Node):
    def __init__(self):
        super().__init__('teleop_node')

        # 读取配置参数
        self.freq = self.declare_parameter('publish_rate', 100).value

        # 轴配置
        axes_linear = self.declare_parameter('axes.linear', [1, 0, 4]).value
        axes_angular = self.declare_parameter('axes.angular', [3, 2, 0]).value
        axes_jog = self.declare_parameter('axes.jog', [6, 7]).value
        self.axes_indices = np.array(axes_linear + axes_angular + axes_jog)  # [6+2] 索引数组

        # 6DOF配置数组 [linear_xyz + angular_xyz]
        deadzone_linear = self.declare_parameter('deadzone.linear', [0.05, 0.05, 0.05]).value
        deadzone_angular = self.declare_parameter('deadzone.angular', [0.05, 0.05, 0.05]).value
        self.deadzone = np.array(deadzone_linear + deadzone_angular)

        scale_linear = self.declare_parameter('scale.linear', [1.0, 1.0, 1.0]).value
        scale_angular = self.declare_parameter('scale.angular', [1.0, 1.0, 1.0]).value
        self.scale = np.array(scale_linear + scale_angular)

        direction_linear = self.declare_parameter('direction.linear', [1.0, 1.0, 1.0]).value
        direction_angular = self.declare_parameter('direction.angular', [1.0, 1.0, 1.0]).value
        self.direction = np.array(direction_linear + direction_angular)

        self.scale_jog = np.array(self.declare_parameter('scale.jog', [0.05, 0.05]).value)

        # 按钮配置
        self.buttons = {
            'start': self.declare_parameter('buttons.start', 7).value,
            'reset': self.declare_parameter('buttons.reset', 6).value,
            'sucker': self.declare_parameter('buttons.sucker', 0).value,
            'record': self.declare_parameter('buttons.record', 3).value
        }

        # 创建发布器和订阅器
        self.twist_pub = self.create_publisher(TwistStamped, '/mofo_teleop/xbox/target_twist', 10)
        self.button_pubs = {name: self.create_publisher(Int32, f'/mofo_teleop/{name}', 10)
                           for name in self.buttons.keys()}

        self.subscription = self.create_subscription(Joy, 'joy', self.joy_callback, 10)
        self.timer = self.create_timer(1.0 / self.freq, self.publish_cmd)

        # 状态变量
        self.last_joy = None
        self.last_reset_state = False

        # 轨迹规划器 - 传入dt = 1/freq
        dt = 1.0 / self.freq
        trajectory_config = {
            'max_velocity': self.declare_parameter('trajectory.max_velocity', [0.5, 0.5, 0.3, 1.0, 1.0, 1.0]).value,
            'max_acceleration': self.declare_parameter('trajectory.max_acceleration', [2.0, 2.0, 1.5, 3.0, 3.0, 3.0]).value,
            'max_jerk': self.declare_parameter('trajectory.max_jerk', [10.0, 10.0, 8.0, 15.0, 15.0, 15.0]).value,
            'history_length': self.declare_parameter('trajectory.history_length', 5).value
        }
        self.trajectory_planner = TrajectoryPlanner(dt, trajectory_config)

        self.get_logger().info(f"TrajectoryPlanner initialized with dt={dt:.4f}s (freq={self.freq}Hz)")

    def joy_callback(self, msg):
        self.last_joy = msg

    def publish_cmd(self):
        if self.last_joy is None:
            return

        # 检查reset按钮
        reset_pressed = self.safe_button_value(self.last_joy.buttons, self.buttons["reset"])
        if reset_pressed and not self.last_reset_state:
            self.trajectory_planner.reset()
        self.last_reset_state = reset_pressed

        # 高效向量化处理：一次性获取所有轴值
        axes_values = np.array([self.safe_axis_value(self.last_joy.axes, idx) for idx in self.axes_indices[:6]])

        # 向量化死区处理和缩放
        deadzone_mask = np.abs(axes_values) >= self.deadzone
        processed_values = np.where(deadzone_mask,
                                   np.sign(axes_values) * (3 * ((np.abs(axes_values) - self.deadzone) / (1.0 - self.deadzone))**2 -
                                                          2 * ((np.abs(axes_values) - self.deadzone) / (1.0 - self.deadzone))**3),
                                   0.0) * self.scale

        # 添加jog输入到线速度
        jog_values = np.array([self.safe_axis_value(self.last_joy.axes, self.axes_indices[6]) * self.scale_jog[0],
                              self.safe_axis_value(self.last_joy.axes, self.axes_indices[7]) * self.scale_jog[1], 0])
        processed_values[:3] += jog_values

        # 应用方向乘数并强制angular_y=0
        target_velocity = processed_values * self.direction
        target_velocity[4] = 0

        # 轨迹规划和发布
        planned_velocity = self.trajectory_planner.plan_velocity_trajectory(target_velocity)

        # 创建并发布twist消息
        twist_stamped = TwistStamped()
        twist_stamped.header.stamp = self.get_clock().now().to_msg()
        twist_stamped.twist.linear.x, twist_stamped.twist.linear.y, twist_stamped.twist.linear.z = planned_velocity[:3]
        twist_stamped.twist.angular.x, twist_stamped.twist.angular.y, twist_stamped.twist.angular.z = planned_velocity[3:]
        self.twist_pub.publish(twist_stamped)

        # 批量发布按钮状态
        for name, idx in self.buttons.items():
            self.button_pubs[name].publish(Int32(data=self.safe_button_value(self.last_joy.buttons, idx)))


    @staticmethod
    def apply_deadzone(value, deadzone):
        """Smoothstep死区处理"""
        if abs(value) < deadzone:
            return 0.0

        sign = 1.0 if value > 0 else -1.0
        u = max(0.0, min((abs(value) - deadzone) / (1.0 - deadzone), 1.0))
        return sign * (3 * u * u - 2 * u * u * u)

    @staticmethod
    def safe_axis_value(axes, index):
        if index is None or index >= len(axes) or index < 0:
            return 0.0
        return axes[index]

    @staticmethod
    def safe_button_value(buttons, index):
        if index is None or index >= len(buttons) or index < 0:
            return 0
        return buttons[index]

def main(args=None):
    rclpy.init(args=args)
    node = TeleopNode()
    rclpy.spin(node)
    rclpy.shutdown()


if __name__ == '__main__':
    main()
