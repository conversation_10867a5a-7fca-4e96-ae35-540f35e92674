#!/usr/bin/env python3
import rclpy
from rclpy.node import Node
from sensor_msgs.msg import Joy
from geometry_msgs.msg import TwistStamped
# from std_msgs.msg import Bool
# from std_msgs.msg import Int32MultiArray
# from std_msgs.msg import Header
from std_msgs.msg import Int32
import numpy as np
from collections import deque


class TrajectoryPlanner:
    """S曲线轨迹规划器 - 6DOF版本"""

    def __init__(self):
        # 轨迹规划参数 [linear_x, linear_y, linear_z, angular_x, angular_y, angular_z]
        self.max_velocity = np.array([0.5, 0.5, 0.3, 1.0, 1.0, 1.0])  # m/s, rad/s
        self.max_acceleration = np.array([2.0, 2.0, 1.5, 3.0, 3.0, 3.0])  # m/s², rad/s²
        self.max_jerk = np.array([10.0, 10.0, 8.0, 15.0, 15.0, 15.0])  # m/s³, rad/s³

        # 状态变量
        self.current_velocity = np.zeros(6)
        self.current_acceleration = np.zeros(6)
        self.dt = 0.01  # 10ms时间间隔，对应100Hz
        self.velocity_history = deque(maxlen=5)

    def plan_velocity_trajectory(self, target_vel_array):
        """S曲线速度轨迹规划"""
        target_vel = np.array(target_vel_array)
        target_vel = np.clip(target_vel, -self.max_velocity, self.max_velocity)
        planned_velocity = self.current_velocity.copy()

        for i in range(6):
            vel_diff = target_vel[i] - self.current_velocity[i]
            max_accel_step = self.max_acceleration[i] * self.dt
            max_jerk_step = self.max_jerk[i] * self.dt * self.dt

            if abs(vel_diff) <= max_accel_step:
                planned_velocity[i] = target_vel[i]
                self.current_acceleration[i] = vel_diff / self.dt
            else:
                accel_step = max_accel_step if vel_diff > 0 else -max_accel_step
                accel_diff = accel_step - self.current_acceleration[i] * self.dt
                if abs(accel_diff) > max_jerk_step:
                    accel_step = self.current_acceleration[i] * self.dt + \
                               (max_jerk_step if accel_diff > 0 else -max_jerk_step)
                planned_velocity[i] = self.current_velocity[i] + accel_step
                self.current_acceleration[i] = accel_step / self.dt

        self.current_velocity = planned_velocity.copy()
        self.velocity_history.append(planned_velocity.copy())

        if len(self.velocity_history) > 1:
            return np.mean(self.velocity_history, axis=0)
        return planned_velocity

    def emergency_stop(self):
        """紧急停止"""
        self.current_velocity.fill(0)
        self.current_acceleration.fill(0)
        self.velocity_history.clear()
        return self.current_velocity

    def reset(self):
        """重置轨迹规划器状态"""
        self.current_velocity.fill(0)
        self.current_acceleration.fill(0)
        self.velocity_history.clear()



class TeleopNode(Node):
    def __init__(self):
        super().__init__('teleop_node')

        self.declare_parameters(
            namespace='',
            parameters=[
                ('publish_rate', 100),
                ('axes.linear', [1, 0, 4]),
                ('axes.angular', [3, 2, 0]),
                ('axes.jog', [6, 7]),
                ('deadzone.linear', [0.05, 0.05, 0.05]),
                ('deadzone.angular', [0.05, 0.05, 0.05]),
                ('direction.linear', [1.0, 1.0, 1.0]),
                ('direction.angular', [1.0, 1.0, 1.0]),
                ('scale_linear', [1.0, 2.0, 3.0]),
                ('scale_angular', [4.0, 5.0, 6.0]),
                ('scale_linear_jog', [1.0, 1.0]),
                ('buttons.start', 0),
                ('buttons.reset', 1),
                ('buttons.sucker', 2),
                ('buttons.record', 3),
            ]
        )
        
        self.freq = self.get_parameter("publish_rate").get_parameter_value().integer_value
        linear_axes = self.get_parameter("axes.linear").get_parameter_value().integer_array_value
        angular_axes = self.get_parameter("axes.angular").get_parameter_value().integer_array_value
        jog_axes = self.get_parameter("axes.jog").get_parameter_value().integer_array_value

        self.axes_map = {
            "linear_x": linear_axes[0],
            "linear_y": linear_axes[1],
            "linear_z": linear_axes[2],
            "angular_x": angular_axes[0],
            "angular_y": angular_axes[1],
            "angular_z": angular_axes[2],
            "jog_x": jog_axes[0],
            "jog_y": jog_axes[1]
        }

        self.deadzone_linear = self.get_parameter("deadzone.linear").get_parameter_value().double_array_value
        self.deadzone_angular = self.get_parameter("deadzone.angular").get_parameter_value().double_array_value
        self.scale_linear = self.get_parameter("scale_linear").get_parameter_value().double_array_value
        self.scale_angular = self.get_parameter("scale_angular").get_parameter_value().double_array_value
        self.scale_linear_jog = self.get_parameter("scale_linear_jog").get_parameter_value().double_array_value
        self.direction_linear = self.get_parameter("direction.linear").get_parameter_value().double_array_value
        self.direction_angular = self.get_parameter("direction.angular").get_parameter_value().double_array_value

        self.buttons_map = {
            "start": self.get_parameter("buttons.start").get_parameter_value().integer_value,
            "reset": self.get_parameter("buttons.reset").get_parameter_value().integer_value,
            "sucker": self.get_parameter("buttons.sucker").get_parameter_value().integer_value,
            "record": self.get_parameter("buttons.record").get_parameter_value().integer_value
        }

        self.twist_pub = self.create_publisher(TwistStamped, '/mofo_teleop/xbox/target_twist', 10)

        # 为每个按钮创建一个Int32发布器
        self.button_pubs = {}
        for button_name in self.buttons_map.keys():
            topic_name = f'/mofo_teleop/{button_name}'
            self.button_pubs[button_name] = self.create_publisher(Int32, topic_name, 10)
            self.get_logger().info(f"Created Int32 publisher for button: {topic_name}")

        self.subscription = self.create_subscription(Joy, 'joy', self.joy_callback, 10)
        self.timer = self.create_timer(1.0 / self.freq, self.publish_cmd)
        self.last_joy = None

        # 添加轨迹规划器
        self.trajectory_planner = TrajectoryPlanner()
        self.last_reset_state = False

    def joy_callback(self, msg):
        self.last_joy = msg

    def publish_cmd(self):
        if self.last_joy is None:
            return

        now = self.get_clock().now().to_msg()

        # 检查reset按钮状态
        reset_pressed = self.safe_button_value(self.last_joy.buttons, self.buttons_map["reset"])
        if reset_pressed and not self.last_reset_state:
            # reset按钮被按下，重置轨迹规划器
            self.trajectory_planner.reset()
            self.get_logger().info("Trajectory planner reset")
        self.last_reset_state = reset_pressed

        # 计算目标速度
        linear_x = self.apply_deadzone(self.safe_axis_value(self.last_joy.axes, self.axes_map["linear_x"]), self.deadzone_linear[0]) * self.scale_linear[0]
        linear_y = self.apply_deadzone(self.safe_axis_value(self.last_joy.axes, self.axes_map["linear_y"]), self.deadzone_linear[1]) * self.scale_linear[1]
        linear_z = self.apply_deadzone(self.safe_axis_value(self.last_joy.axes, self.axes_map["linear_z"]), self.deadzone_linear[2]) * self.scale_linear[2]

        angular_x = self.apply_deadzone(self.safe_axis_value(self.last_joy.axes, self.axes_map["angular_x"]), self.deadzone_angular[0]) * self.scale_angular[0]
        angular_y = self.apply_deadzone(self.safe_axis_value(self.last_joy.axes, self.axes_map["angular_y"]), self.deadzone_angular[1]) * self.scale_angular[1]
        angular_z = self.apply_deadzone(self.safe_axis_value(self.last_joy.axes, self.axes_map["angular_z"]), self.deadzone_angular[2]) * self.scale_angular[2]

        jog_x = self.safe_axis_value(self.last_joy.axes, self.axes_map["jog_x"]) * self.scale_linear_jog[0]
        jog_y = self.safe_axis_value(self.last_joy.axes, self.axes_map["jog_y"]) * self.scale_linear_jog[1]

        # apply direction multipliers
        target_linear_x = (linear_x + jog_x) * self.direction_linear[0]
        target_linear_y = (linear_y + jog_y) * self.direction_linear[1]
        target_linear_z = linear_z * self.direction_linear[2]

        target_angular_x = angular_x * self.direction_angular[0]
        target_angular_y = angular_y * self.direction_angular[1]
        target_angular_z = angular_z * self.direction_angular[2]
        target_angular_y = 0

        # 构建目标速度数组 [linear_x, linear_y, linear_z, angular_x, angular_y, angular_z]
        target_velocity = [target_linear_x, target_linear_y, target_linear_z,
                          target_angular_x, target_angular_y, target_angular_z]

        # 使用轨迹规划器规划速度
        planned_velocity = self.trajectory_planner.plan_velocity_trajectory(target_velocity)

        # 创建并发布TwistStamped消息
        twist_stamped = TwistStamped()
        twist_stamped.header.stamp = now
        twist = twist_stamped.twist

        twist.linear.x = planned_velocity[0]
        twist.linear.y = planned_velocity[1]
        twist.linear.z = planned_velocity[2]

        twist.angular.x = planned_velocity[3]
        twist.angular.y = planned_velocity[4]
        twist.angular.z = planned_velocity[5]

        self.twist_pub.publish(twist_stamped)
        
        # 发布每个按钮的Int32消息
        for button_name, button_index in self.buttons_map.items():
            button_value = self.safe_button_value(self.last_joy.buttons, button_index)
            button_msg = Int32()
            button_msg.data = button_value
            self.button_pubs[button_name].publish(button_msg)


    @staticmethod
    def apply_deadzone(value, deadzone):
        #if abs(value) < deadzone:
        #    return 0.0
        #sign = 1.0 if value > 0 else -1.0
        #return sign * (abs(value) - deadzone) / (1.0 - deadzone)
                
        # 1. 死区判断 (Deadzone check)        
        # 如果 |value| < deadzone，则直接输出 0，避免抖动。
        # If |value| < deadzone, return 0 to avoid jitter.
        if abs(value) < deadzone:
            return 0.0

        # 2. 归一化到 [0, 1] (Normalize to [0,1])
        # 先去掉死区部分，再除以 (1 - deadzone) 将区间 [deadzone, 1] 映射到 [0, 1]。
        sign = 1.0 if value > 0 else -1.0
        u = (abs(value) - deadzone) / (1.0 - deadzone)
        # 确保 u 在 [0, 1] 范围内
        u = max(0.0, min(u, 1.0))

        # 3. Smoothstep 映射本体 (Smoothstep mapping)
        # f(u) = 3u^2 - 2u^3，其中 u ∈ [0, 1]
        # f(u) 在 0 处导数为 0，在 1 处导数也为 0，中间近似线性。
        y_norm = 3 * (u ** 2) - 2 * (u ** 3)

        # 4. 恢复符号并返回 (Restore sign and return)
        return sign * y_norm

    @staticmethod
    def safe_axis_value(axes, index):
        if index is None or index >= len(axes) or index < 0:
            return 0.0
        return axes[index]

    @staticmethod
    def safe_button_value(buttons, index):
        if index is None or index >= len(buttons) or index < 0:
            return 0
        return buttons[index]

def main(args=None):
    rclpy.init(args=args)
    node = TeleopNode()
    rclpy.spin(node)
    rclpy.shutdown()


if __name__ == '__main__':
    main()
