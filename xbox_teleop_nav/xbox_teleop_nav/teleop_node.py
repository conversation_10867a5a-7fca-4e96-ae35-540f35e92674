#!/usr/bin/env python3
import rclpy
from rclpy.node import Node
from sensor_msgs.msg import Joy
from geometry_msgs.msg import TwistStamped
from std_msgs.msg import Int32
import numpy as np
from collections import deque


class TrajectoryPlanner:
    """S曲线轨迹规划器 - 6DOF版本"""

    def __init__(self):
        # 轨迹规划参数 [linear_x, linear_y, linear_z, angular_x, angular_y, angular_z]
        self.max_velocity = np.array([0.5, 0.5, 0.3, 1.0, 1.0, 1.0])
        self.max_acceleration = np.array([2.0, 2.0, 1.5, 3.0, 3.0, 3.0])
        self.max_jerk = np.array([10.0, 10.0, 8.0, 15.0, 15.0, 15.0])

        # 状态变量
        self.current_velocity = np.zeros(6)
        self.current_acceleration = np.zeros(6)
        self.dt = 0.01
        self.velocity_history = deque(maxlen=5)

    def plan_velocity_trajectory(self, target_vel_array):
        """S曲线速度轨迹规划 - 向量化优化版本"""
        target_vel = np.clip(target_vel_array, -self.max_velocity, self.max_velocity)
        vel_diff = target_vel - self.current_velocity
        max_accel_step = self.max_acceleration * self.dt
        max_jerk_step = self.max_jerk * self.dt * self.dt

        # 向量化处理
        small_diff_mask = np.abs(vel_diff) <= max_accel_step
        planned_velocity = np.where(small_diff_mask, target_vel, self.current_velocity)

        # 处理大差值情况
        large_diff_mask = ~small_diff_mask
        if np.any(large_diff_mask):
            accel_step = np.where(vel_diff > 0, max_accel_step, -max_accel_step)
            accel_diff = accel_step - self.current_acceleration * self.dt
            jerk_limited = np.where(np.abs(accel_diff) > max_jerk_step,
                                  self.current_acceleration * self.dt + np.sign(accel_diff) * max_jerk_step,
                                  accel_step)
            planned_velocity = np.where(large_diff_mask,
                                      self.current_velocity + jerk_limited,
                                      planned_velocity)
            self.current_acceleration = np.where(large_diff_mask, jerk_limited / self.dt, vel_diff / self.dt)

        self.current_velocity = planned_velocity
        self.velocity_history.append(planned_velocity.copy())

        return np.mean(self.velocity_history, axis=0) if len(self.velocity_history) > 1 else planned_velocity

    def reset(self):
        """重置轨迹规划器状态"""
        self.current_velocity.fill(0)
        self.current_acceleration.fill(0)
        self.velocity_history.clear()



class TeleopNode(Node):
    def __init__(self):
        super().__init__('teleop_node')

        # 批量声明参数
        params = {
            'publish_rate': 100,
            'axes.linear': [1, 0, 4], 'axes.angular': [3, 2, 0], 'axes.jog': [6, 7],
            'deadzone.linear': [0.05, 0.05, 0.05], 'deadzone.angular': [0.05, 0.05, 0.05],
            'direction.linear': [1.0, 1.0, 1.0], 'direction.angular': [1.0, 1.0, 1.0],
            'scale_linear': [1.0, 2.0, 3.0], 'scale_angular': [4.0, 5.0, 6.0],
            'scale_linear_jog': [1.0, 1.0],
            'buttons.start': 0, 'buttons.reset': 1, 'buttons.sucker': 2, 'buttons.record': 3,
        }
        self.declare_parameters(namespace='', parameters=list(params.items()))

        # 简化参数读取
        self.freq = self.get_parameter("publish_rate").value
        linear_axes = self.get_parameter("axes.linear").value
        angular_axes = self.get_parameter("axes.angular").value
        jog_axes = self.get_parameter("axes.jog").value

        # 轴映射
        self.axes_map = dict(zip(['linear_x', 'linear_y', 'linear_z', 'angular_x', 'angular_y', 'angular_z', 'jog_x', 'jog_y'],
                                linear_axes + angular_axes + jog_axes))

        # 配置数组
        self.deadzone = np.array(self.get_parameter("deadzone.linear").value + self.get_parameter("deadzone.angular").value)
        self.scale = np.array(self.get_parameter("scale_linear").value + self.get_parameter("scale_angular").value)
        self.direction = np.array(self.get_parameter("direction.linear").value + self.get_parameter("direction.angular").value)
        self.scale_jog = np.array(self.get_parameter("scale_linear_jog").value)

        # 按钮映射
        self.buttons_map = {name: self.get_parameter(f"buttons.{name}").value
                           for name in ['start', 'reset', 'sucker', 'record']}

        self.twist_pub = self.create_publisher(TwistStamped, '/mofo_teleop/xbox/target_twist', 10)

        # 为每个按钮创建一个Int32发布器
        self.button_pubs = {}
        for button_name in self.buttons_map.keys():
            topic_name = f'/mofo_teleop/{button_name}'
            self.button_pubs[button_name] = self.create_publisher(Int32, topic_name, 10)
            self.get_logger().info(f"Created Int32 publisher for button: {topic_name}")

        self.subscription = self.create_subscription(Joy, 'joy', self.joy_callback, 10)
        self.timer = self.create_timer(1.0 / self.freq, self.publish_cmd)
        self.last_joy = None

        # 添加轨迹规划器
        self.trajectory_planner = TrajectoryPlanner()
        self.last_reset_state = False

    def joy_callback(self, msg):
        self.last_joy = msg

    def publish_cmd(self):
        if self.last_joy is None:
            return

        # 检查reset按钮
        reset_pressed = self.safe_button_value(self.last_joy.buttons, self.buttons_map["reset"])
        if reset_pressed and not self.last_reset_state:
            self.trajectory_planner.reset()
        self.last_reset_state = reset_pressed

        # 向量化计算目标速度
        axes_indices = [self.axes_map[key] for key in ['linear_x', 'linear_y', 'linear_z', 'angular_x', 'angular_y', 'angular_z']]
        raw_values = np.array([self.safe_axis_value(self.last_joy.axes, idx) for idx in axes_indices])

        # 应用死区和缩放
        processed_values = np.array([self.apply_deadzone(val, dz) for val, dz in zip(raw_values, self.deadzone)]) * self.scale

        # 添加jog输入
        jog_values = np.array([self.safe_axis_value(self.last_joy.axes, self.axes_map["jog_x"]) * self.scale_jog[0],
                              self.safe_axis_value(self.last_joy.axes, self.axes_map["jog_y"]) * self.scale_jog[1], 0])
        processed_values[:3] += jog_values

        # 应用方向乘数
        target_velocity = processed_values * self.direction
        target_velocity[4] = 0  # 强制angular_y为0

        # 轨迹规划和发布
        planned_velocity = self.trajectory_planner.plan_velocity_trajectory(target_velocity)

        twist_stamped = TwistStamped()
        twist_stamped.header.stamp = self.get_clock().now().to_msg()
        twist_stamped.twist.linear.x, twist_stamped.twist.linear.y, twist_stamped.twist.linear.z = planned_velocity[:3]
        twist_stamped.twist.angular.x, twist_stamped.twist.angular.y, twist_stamped.twist.angular.z = planned_velocity[3:]

        self.twist_pub.publish(twist_stamped)

        # 批量发布按钮状态
        for name, idx in self.buttons_map.items():
            msg = Int32(data=self.safe_button_value(self.last_joy.buttons, idx))
            self.button_pubs[name].publish(msg)


    @staticmethod
    def apply_deadzone(value, deadzone):
        """Smoothstep死区处理"""
        if abs(value) < deadzone:
            return 0.0

        sign = 1.0 if value > 0 else -1.0
        u = max(0.0, min((abs(value) - deadzone) / (1.0 - deadzone), 1.0))
        return sign * (3 * u * u - 2 * u * u * u)

    @staticmethod
    def safe_axis_value(axes, index):
        if index is None or index >= len(axes) or index < 0:
            return 0.0
        return axes[index]

    @staticmethod
    def safe_button_value(buttons, index):
        if index is None or index >= len(buttons) or index < 0:
            return 0
        return buttons[index]

def main(args=None):
    rclpy.init(args=args)
    node = TeleopNode()
    rclpy.spin(node)
    rclpy.shutdown()


if __name__ == '__main__':
    main()
