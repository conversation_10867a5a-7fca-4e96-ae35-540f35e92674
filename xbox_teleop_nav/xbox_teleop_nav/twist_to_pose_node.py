#!/usr/bin/env python3
import rclpy
from rclpy.node import Node
from geometry_msgs.msg import TwistStamped, PoseStamped
from tf2_ros import TransformBroadcaster
from geometry_msgs.msg import TransformStamped
from std_msgs.msg import Int32
import numpy as np
from scipy.spatial.transform import Rotation as R
import time

class TwistToPoseNode(Node):
    def __init__(self):
        super().__init__('twist_to_pose_node')

        # 简化参数读取
        self.frame_id = self.declare_parameter("frame_id", "tool").value
        self.child_frame = self.declare_parameter("child_frame", "base_link").value
        self.sub_twist_topic = self.declare_parameter("sub_twist_topic", "/mofo_teleop/xbox/target_twist").value
        self.pub_pose_topic = self.declare_parameter("pub_pose_topic", "/mofo_teleop/target_ee_poses").value

        # 速度和位置限制
        self.max_linear = np.array(self.declare_parameter("max_linear", [0.2, 0.2, 0.2]).value)
        self.max_angular = np.array(self.declare_parameter("max_angular", [0.2, 0.2, 0.2]).value)
        self.pos_min = np.array(self.declare_parameter("pos_min", [-1.0, -1.0, -1.0]).value)
        self.pos_max = np.array(self.declare_parameter("pos_max", [1.0, 1.0, 1.0]).value)
        self.euler_min = np.array(self.declare_parameter("euler_min", [-np.pi, -np.pi, -np.pi]).value)
        self.euler_max = np.array(self.declare_parameter("euler_max", [np.pi, np.pi, np.pi]).value)

        # 初始位姿
        init_pos_vals = self.declare_parameter("initial_position", [0.0, 0.0, 0.0]).value
        init_euler_vals = self.declare_parameter("initial_euler", [0.0, 0.0, 0.0]).value
        self.initial_position = np.array(init_pos_vals)
        self.initial_orientation = R.from_euler('xyz', init_euler_vals, degrees=True)

        # 当前位姿
        self.current_position = self.initial_position.copy()
        self.current_orientation = self.initial_orientation
        self.last_time = time.time()

        # 创建订阅和发布
        self.twist_sub = self.create_subscription(TwistStamped, self.sub_twist_topic, self.twist_cb, 10)
        self.reset_sub = self.create_subscription(Int32, '/mofo_teleop/reset', self.reset_cb, 10)
        self.pose_pub = self.create_publisher(PoseStamped, self.pub_pose_topic, 10)
        self.br = TransformBroadcaster(self)

        # 发布定时器
        publish_freq = self.declare_parameter("publish_frequency", 20.0).value
        self.timer = self.create_timer(1.0 / publish_freq, self.publish_pose)

        self.get_logger().info(f"twist_to_pose_node ready: pos={self.initial_position}, euler={init_euler_vals}°")

    def reset_cb(self, msg):
        """Reset回调：当前位姿 = 初始位姿"""
        if msg.data == 1:  # 按钮被按下
            self.current_position = self.initial_position.copy()
            self.current_orientation = self.initial_orientation
            #self.get_logger().info(f"Reset: {self.initial_position}")

    def twist_cb(self, msg):
        """速度积分：位姿 = 速度 * dt + 当前位姿"""
        now = time.time()
        dt = min(now - self.last_time, 0.1)  # 限制dt最大100ms
        self.last_time = now

        # 向量化速度限制和积分
        linear_vel = np.clip([msg.twist.linear.x, msg.twist.linear.y, msg.twist.linear.z],
                           -self.max_linear, self.max_linear)
        angular_vel = np.clip([msg.twist.angular.x, msg.twist.angular.y, msg.twist.angular.z],
                            -self.max_angular, self.max_angular)

        # 位置和姿态积分
        self.current_position += linear_vel * dt
        if np.any(angular_vel):  # 只在有角速度时计算旋转
            self.current_orientation = self.current_orientation * R.from_rotvec(angular_vel * dt)


    def publish_pose(self):
        """发布当前位姿"""
        try:
            # 应用位姿限制
            pos_clamped = np.clip(self.current_position, self.pos_min, self.pos_max)
            euler_clamped = np.clip(self.current_orientation.as_euler('xyz'), self.euler_min, self.euler_max)
            quat = R.from_euler('xyz', euler_clamped).as_quat()

            # 创建位姿消息
            stamp = self.get_clock().now().to_msg()
            pose_msg = PoseStamped()
            pose_msg.header.stamp = stamp
            pose_msg.header.frame_id = self.frame_id
            pose_msg.pose.position.x, pose_msg.pose.position.y, pose_msg.pose.position.z = pos_clamped
            pose_msg.pose.orientation.x, pose_msg.pose.orientation.y, pose_msg.pose.orientation.z, pose_msg.pose.orientation.w = quat

            self.pose_pub.publish(pose_msg)

            # 发布TF变换
            tf_msg = TransformStamped()
            tf_msg.header = pose_msg.header
            tf_msg.child_frame_id = self.child_frame
            tf_msg.transform.translation.x, tf_msg.transform.translation.y, tf_msg.transform.translation.z = pos_clamped
            tf_msg.transform.rotation = pose_msg.pose.orientation
            self.br.sendTransform(tf_msg)

        except Exception:
            pass

def main(args=None):
    rclpy.init(args=args)
    node = TwistToPoseNode()
    rclpy.spin(node)
    rclpy.shutdown()

if __name__ == '__main__':
    main()
