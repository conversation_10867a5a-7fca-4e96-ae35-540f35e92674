#!/usr/bin/env python3
import rclpy
from rclpy.node import Node
from geometry_msgs.msg import TwistStamped, PoseStamped
from tf2_ros import TransformBroadcaster
from geometry_msgs.msg import TransformStamped
from std_msgs.msg import Int32
import numpy as np
from scipy.spatial.transform import Rotation as R
import time

class TwistToPoseNode(Node):
    def __init__(self):
        super().__init__('twist_to_pose_node')

        # 话题配置
        self.twist_topic = self.declare_parameter("topics.twist_input", "/mofo_teleop/xbox/target_twist").value
        self.pose_topic = self.declare_parameter("topics.pose_output", "/mofo_teleop/target_ee_poses").value

        # 坐标系配置
        self.frame_id = self.declare_parameter("frames.parent", "tool").value
        self.child_frame = self.declare_parameter("frames.child", "base_link").value

        # 6D速度限制 [linear_xyz, angular_xyz]
        linear_limits = self.declare_parameter("velocity_limits.linear", [0.1, 0.1, 0.1]).value
        angular_limits = self.declare_parameter("velocity_limits.angular", [0.1, 0.1, 0.1]).value
        self.velocity_limits = np.array(linear_limits + angular_limits)

        # 6D位姿限制
        pos_min = self.declare_parameter("pose_limits.position.min", [-1.0, -1.0, -1.0]).value
        pos_max = self.declare_parameter("pose_limits.position.max", [1.0, 1.0, 1.0]).value
        euler_min = self.declare_parameter("pose_limits.orientation.min", [-np.pi, -np.pi, -np.pi]).value
        euler_max = self.declare_parameter("pose_limits.orientation.max", [np.pi, np.pi, np.pi]).value
        self.pose_limits = np.array([pos_min + euler_min, pos_max + euler_max])  # [min, max] x 6

        # 6D初始位姿
        init_pos = self.declare_parameter("initial_pose.position", [0.0, 0.0, 0.0]).value
        init_euler = self.declare_parameter("initial_pose.orientation", [0.0, 0.0, 0.0]).value
        self.initial_pose = np.array(init_pos + init_euler)  # [x,y,z,roll,pitch,yaw]
        self.initial_position = np.array(init_pos)
        self.initial_orientation = R.from_euler('xyz', init_euler, degrees=True)

        # 当前6D位姿状态 [x,y,z,roll,pitch,yaw]
        self.current_pose = self.initial_pose.copy()
        self.current_position = self.initial_position.copy()
        self.current_orientation = self.initial_orientation
        self.last_time = time.time()

        # 创建订阅和发布
        self.twist_sub = self.create_subscription(TwistStamped, self.twist_topic, self.twist_cb, 10)
        self.reset_sub = self.create_subscription(Int32, '/mofo_teleop/reset', self.reset_cb, 10)
        self.pose_pub = self.create_publisher(PoseStamped, self.pose_topic, 10)
        self.br = TransformBroadcaster(self)

        # 发布定时器
        publish_freq = self.declare_parameter("publish_frequency", 100.0).value
        self.timer = self.create_timer(1.0 / publish_freq, self.publish_pose)

        self.get_logger().info(f"6DOF pose node ready: {init_pos} + {init_euler}°")

    def reset_cb(self, msg):
        """Reset回调：重置到初始6D位姿"""
        if msg.data == 1:
            self.current_pose = self.initial_pose.copy()
            self.current_position = self.initial_position.copy()
            self.current_orientation = self.initial_orientation

    def twist_cb(self, msg):
        """6DOF速度积分：位姿 += 速度 * dt"""
        now = time.time()
        dt = min(now - self.last_time, 0.1)  # 限制dt最大100ms
        self.last_time = now

        # 6D速度向量 [linear_xyz, angular_xyz]
        velocity_6d = np.array([msg.twist.linear.x, msg.twist.linear.y, msg.twist.linear.z,
                               msg.twist.angular.x, msg.twist.angular.y, msg.twist.angular.z])

        # 向量化速度限制
        velocity_6d = np.clip(velocity_6d, -self.velocity_limits, self.velocity_limits)

        # 6D积分
        self.current_position += velocity_6d[:3] * dt
        if np.any(velocity_6d[3:]):  # 只在有角速度时计算旋转
            self.current_orientation = self.current_orientation * R.from_rotvec(velocity_6d[3:] * dt)

        # 更新6D位姿状态
        euler = self.current_orientation.as_euler('xyz')
        self.current_pose = np.concatenate([self.current_position, euler])


    def publish_pose(self):
        """发布6D位姿"""
        try:
            # 6D位姿限制 [x,y,z,roll,pitch,yaw]
            pose_clamped = np.clip(self.current_pose, self.pose_limits[0], self.pose_limits[1])
            pos_clamped = pose_clamped[:3]
            euler_clamped = pose_clamped[3:]
            quat = R.from_euler('xyz', euler_clamped).as_quat()

            # 高效消息创建
            stamp = self.get_clock().now().to_msg()

            # PoseStamped消息
            pose_msg = PoseStamped()
            pose_msg.header.stamp = stamp
            pose_msg.header.frame_id = self.frame_id
            pose_msg.pose.position.x, pose_msg.pose.position.y, pose_msg.pose.position.z = pos_clamped
            pose_msg.pose.orientation.x, pose_msg.pose.orientation.y, pose_msg.pose.orientation.z, pose_msg.pose.orientation.w = quat
            self.pose_pub.publish(pose_msg)

            # TF变换
            tf_msg = TransformStamped()
            tf_msg.header = pose_msg.header
            tf_msg.child_frame_id = self.child_frame
            tf_msg.transform.translation.x, tf_msg.transform.translation.y, tf_msg.transform.translation.z = pos_clamped
            tf_msg.transform.rotation = pose_msg.pose.orientation
            self.br.sendTransform(tf_msg)

        except Exception:
            pass

def main(args=None):
    rclpy.init(args=args)
    node = TwistToPoseNode()
    rclpy.spin(node)
    rclpy.shutdown()

if __name__ == '__main__':
    main()
