#!/usr/bin/env python3
import rclpy
from rclpy.node import Node
from geometry_msgs.msg import TwistStamped, PoseStamped
from tf2_ros import TransformBroadcaster
from geometry_msgs.msg import TransformStamped
from std_msgs.msg import Int32
import numpy as np
from scipy.spatial.transform import Rotation as R
import time

class TwistToPoseNode(Node):
    def __init__(self):
        super().__init__('twist_to_pose_node')

        # 从参数服务器读取frame_id和child_frame_id参数，提供默认值
        self.frame_id = self.declare_parameter("frame_id", "tool").get_parameter_value().string_value
        self.child_frame = self.declare_parameter("child_frame", "base_link").get_parameter_value().string_value

        # Topics from parameters
        self.sub_twist_topic = self.declare_parameter("sub_twist_topic", "/mofo_teleop/xbox/target_twist").get_parameter_value().string_value
        self.pub_pose_topic = self.declare_parameter("pub_pose_topic", "/mofo_teleop/target_ee_poses").get_parameter_value().string_value

        # 最大速度限制：线速度和角速度六维数组参数
        max_linear_vals = self.declare_parameter("max_linear", [0.2, 0.2, 0.2]).get_parameter_value().double_array_value
        max_angular_vals = self.declare_parameter("max_angular", [0.2, 0.2, 0.2]).get_parameter_value().double_array_value
        self.max_linear = np.array(max_linear_vals)
        self.max_angular = np.array(max_angular_vals)

        # 6D空间限位保护参数: 位置限位和姿态限位
        pos_min_vals = self.declare_parameter("pos_min", [-1.0, -1.0, -1.0]).get_parameter_value().double_array_value
        pos_max_vals = self.declare_parameter("pos_max", [1.0, 1.0, 1.0]).get_parameter_value().double_array_value
        self.pos_min = np.array(pos_min_vals)
        self.pos_max = np.array(pos_max_vals)
        euler_min_vals = self.declare_parameter("euler_min", [-np.pi, -np.pi, -np.pi]).get_parameter_value().double_array_value
        euler_max_vals = self.declare_parameter("euler_max", [np.pi, np.pi, np.pi]).get_parameter_value().double_array_value
        self.euler_min = np.array(euler_min_vals)
        self.euler_max = np.array(euler_max_vals)

        # 初始位姿配置参数（6D位姿：位置+姿态）
        init_pos_vals = self.declare_parameter("initial_position", [0.0, 0.0, 0.0]).get_parameter_value().double_array_value
        init_euler_vals = self.declare_parameter("initial_euler", [0.0, 0.0, 0.0]).get_parameter_value().double_array_value
        self.initial_position = np.array(init_pos_vals)
        self.initial_orientation = R.from_euler('xyz', init_euler_vals, degrees=True)

        # 当前位姿 = 初始位姿
        self.current_position = self.initial_position.copy()
        self.current_orientation = self.initial_orientation

        # 时间管理
        self.last_time = time.time()

        # 创建订阅和发布
        self.twist_sub = self.create_subscription(TwistStamped, self.sub_twist_topic, self.twist_cb, 10)
        self.reset_sub = self.create_subscription(Int32, '/mofo_teleop/reset', self.reset_cb, 10)
        self.pose_pub = self.create_publisher(PoseStamped, self.pub_pose_topic, 10)
        self.br = TransformBroadcaster(self)

        # Timer for publishing pose
        self.publish_frequency = self.declare_parameter("publish_frequency", 20.0).get_parameter_value().double_value
        self.timer = self.create_timer(1.0 / self.publish_frequency, self.publish_pose)

        self.get_logger().info(f"Initial position: {self.initial_position}")
        self.get_logger().info(f"Initial orientation (euler): {init_euler_vals} degrees")
        self.get_logger().info("twist_to_pose_node is ready")

    def reset_cb(self, msg):
        """Reset回调：当前位姿 = 初始位姿"""
        if msg.data == 1:  # 按钮被按下
            self.current_position = self.initial_position.copy()
            self.current_orientation = self.initial_orientation
            #self.get_logger().info(f"Reset: {self.initial_position}")

    def twist_cb(self, msg):
        """订阅速度，发布位姿 = 速度 * dt + 当前位姿"""
        now = time.time()
        dt = now - self.last_time
        self.last_time = now

        # 限制dt避免积分爆炸
        if dt > 0.1:  # 如果时间间隔太大，限制在100ms
            dt = 0.1

        # 应用速度限制
        raw_linear = np.array([msg.twist.linear.x, msg.twist.linear.y, msg.twist.linear.z])
        clamped_linear = np.clip(raw_linear, -self.max_linear, self.max_linear)

        raw_angular = np.array([msg.twist.angular.x, msg.twist.angular.y, msg.twist.angular.z])
        clamped_angular = np.clip(raw_angular, -self.max_angular, self.max_angular)

        # 位置积分：发布位姿 = 速度 * dt + 当前位姿
        delta_position = clamped_linear * dt
        self.current_position += delta_position

        # 姿态积分：角速度积分
        angle = clamped_angular * dt
        delta_rot = R.from_rotvec(angle)
        self.current_orientation = self.current_orientation * delta_rot


    def publish_pose(self):
        """发布当前位姿"""
        try:
            # Apply pose limits
            pos_clamped = np.clip(self.current_position, self.pos_min, self.pos_max)
            # Clamp orientation in Euler angles
            euler = self.current_orientation.as_euler('xyz')
            euler_clamped = np.clip(euler, self.euler_min, self.euler_max)
            ori_clamped = R.from_euler('xyz', euler_clamped)

            # Publish PoseStamped
            pose_msg = PoseStamped()
            pose_msg.header.stamp = self.get_clock().now().to_msg()
            pose_msg.header.frame_id = self.frame_id
            pose_msg.pose.position.x = pos_clamped[0]
            pose_msg.pose.position.y = pos_clamped[1]
            pose_msg.pose.position.z = pos_clamped[2]
            quat = ori_clamped.as_quat()
            pose_msg.pose.orientation.x = quat[0]
            pose_msg.pose.orientation.y = quat[1]
            pose_msg.pose.orientation.z = quat[2]
            pose_msg.pose.orientation.w = quat[3]
            self.pose_pub.publish(pose_msg)

            # Broadcast transform
            t = TransformStamped()
            t.header.stamp = pose_msg.header.stamp
            t.header.frame_id = self.frame_id
            t.child_frame_id = self.child_frame
            t.transform.translation.x = pos_clamped[0]
            t.transform.translation.y = pos_clamped[1]
            t.transform.translation.z = pos_clamped[2]
            t.transform.rotation = pose_msg.pose.orientation
            self.br.sendTransform(t)

        except Exception:
            # Swallow 'publish() to closed topic' error.
            pass

def main(args=None):
    rclpy.init(args=args)
    node = TwistToPoseNode()
    rclpy.spin(node)
    rclpy.shutdown()

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        rclpy.shutdown()
        sys.exit(0)
    except Exception as e:
        print(e)
        sys.exit(1)
