
# 📦 `xbox_teleop_nav` 使用说明

## 📌 简介

`xbox_teleop_nav` 是一个基于 ROS 2 的包，支持使用 Xbox 手柄进行机器人遥控。


`TeleopNode`主要功能是读取手柄（Joy）输入，生成速度指令（TwistStamped）并发布，同时把按键值包装成 Int32 消息发布。

`TwistToPoseNode` 订阅速度消息 TwistStamped 和初始位姿话题 `initial_pose_topic`（ PoseStamped），基于初始位姿对速度进行积分，计算当前位姿，并发布对应的 PoseStamped 消息。同时，该节点通过 TF 广播相应的坐标变换。

---

## 🛠️ 安装依赖

```bash
sudo apt install ros-humble-teleop-twist-joy
```

---

## 🔧 构建包

```bash
colcon build --packages-select xbox_teleop_nav --symlink-install
source install/setup.bash
```

---

## 🚀 启动节点

```bash
ros2 launch xbox_teleop_nav teleop_pose.launch.py
```

---

## 🎮 手柄按键/轴映射表

| 操作             | 标号 | 类型 | 值范围/含义     | 操作含义         |
|------------------|------|------|------------------|------------------|
| 左摇杆 左右      | a0   | 轴   | 左负右正         | y                |
| 左杆 上下        | a1   | 轴   | 上负下正         | x                |
| 右摇杆 左右      | a3   | 轴   | 左负右正         | z                |
| 右杆 上下        | a4   | 轴   | 上负下正         | Rz               |
| 十字点动左右     | a6   | 轴   | 左负右正         | Y点动微调        |
| 十字点动上下     | a7   | 轴   | 上负下正         | X点动微调        |
| A                | b0   | 按钮 | 按下1，不按0     | start            |
| B                | b1   | 按钮 | 按下1，不按0     | reset            |
| X                | b2   | 按钮 | 按下1，不按0     | sucker           |
| Y                | b3   | 按钮 | 按下1，不按0     | record           |
| LB               | b4   | 按钮 | 按下1，不按0     |                  |
| RB               | b5   | 按钮 | 按下1，不按0     |                  |
| Select           | b6   | 按钮 | 按下1，不按0     |                  |
| Start            | b7   | 按钮 | 按下1，不按0     |                  |
| Left trigger     | a2   | 轴   | 按下1，松开-1    |               |
| Right trigger    | a5   | 轴   | 按下1，松开-1    |               |
| M1               | b8   | 按钮 | 按下1，不按0     |                  |
| M2               | b9   | 按钮 | 按下1，不按0     |                  |

---

## ⚙️ 配置参数说明（YAML 文件）

配置文件一般位于 `config/joystick_config.yaml` 和 `config/twist_config.yaml` 等路径下。

### 🔧 `joystick_config.yaml` 参数说明

```yaml
teleop_node:
  ros__parameters:
    publish_rate: 100                           # 发布频率 Hz
    axes.linear: [1, 0, 4]                      # [x, y, z] 对应轴索引
    axes.angular: [2, 0, 3]                     # [x, y, z] 对应轴索引
    axes.jog: [7, 6]                            # [x, y] 点动轴索引
    deadzone.linear: [0.05, 0.05, 0.05]         # 线速度死区范围
    deadzone.angular: [0.05, 0.05, 0.05]        # 角速度死区范围
    direction.linear: [1.0, 1.0, 1.0]           # 线速度方向 [-1, 1]
    direction.angular: [1.0, 1.0, 1.0]          # 角速度方向 [-1, 1]
    scale_linear: [2.0, 1.0, 1.0]               # 线速度方向的缩放
    scale_angular: [0.0, 0.0, 1.0]              # 角速度方向的缩放
    scale_linear_jog: [0.1, 0.1]                # 点动线速度方向的缩放
    buttons.start: 0                            # 对应 A 键的索引  
    buttons.reset: 1                            # 对应 B 键的索引   
    buttons.sucker: 2                           # 对应 X 键的索引   
    buttons.record: 3                           # 对应 Y 键的索引   
```

### 🔧 `twist_config.yaml` 参数说明

```yaml
twist_to_pose_node:
  ros__parameters:
    frame_id: "tool"                          # 要发布的 tf 的父坐标系 (暂时无用)
    child_frame: "base_link"                  # tf 的子坐标系（通常是机器人底盘，暂时无用）
    initial_pose_topic: "/initial_pose"       # 需要外部设置的初始位姿话题
    max_linear: [0.2, 0.2, 0.2]               # 最大线速度 m/s (x, y, z)
    max_angular: [0.1, 0.1, 0.1]              # 最大角速度 rad/s (x, y, z)
```

---
## 🧩 订阅的话题

| 话题名             | 类型                                 | 描述                         |
|--------------------|--------------------------------------|------------------------------|
| `/initial_pose`    | `geometry_msgs/PoseStamped`          | 带时间戳的位姿状态 (通过`twist_config.yaml` 的 `initial_pose_topic` 传入)   |


## 🧩 发布的话题

| 话题名             | 类型                                 | 描述                         |
|--------------------|--------------------------------------|------------------------------|
| `/mofo_teleop/xbox/target_twist`    | `geometry_msgs/TwistStamped`          | 带时间戳的速度控制指令      |
| `/mofo_teleop/target_ee_poses`      | `geometry_msgs/PoseStamped`           | 带时间戳的位姿控制指令      |
| `/mofo_teleop/start`                | `Int32`                               | 按钮状态         |
| `/mofo_teleop/reset`                | `Int32`                               | 按钮状态         |
| `/mofo_teleop/sucker`               | `Int32`                               | 按钮状态         |
| `/mofo_teleop/record`               | `Int32`                               | 按钮状态         |



## 📄 文件结构示意

```
xbox_teleop_nav/
├── config/
│   ├── joystick_config.yaml
│   └── twist_config.yaml
├── launch/
│   └── teleop_pose.launch.py
├── resource
├── xbox_teleop_nav/
│   ├── teleop_node.py
│   └── twist_to_pose_node.py
├── setup.py
├── setup.cfg
├── package.xml
├── README.md
```
