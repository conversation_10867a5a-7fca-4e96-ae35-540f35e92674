
# 📦 `xbox_teleop_nav` 使用说明

## 📌 简介

`xbox_teleop_nav` 是一个基于 ROS 2 的高性能6DOF机器人遥控包，支持使用 Xbox 手柄进行精确的位置和姿态控制。

### 🚀 核心特性

- **6DOF控制**: 3D位置 + 3D姿态统一控制
- **S曲线轨迹规划**: 平滑的速度变化，避免突然加减速
- **向量化计算**: 高效的NumPy向量化处理，提升执行效率
- **统一配置**: 单一配置文件管理所有参数
- **相对积分定位**: 支持动态重置起点的位置积分

### 🔧 节点功能

**`TeleopNode`**:
- 读取手柄（Joy）输入，应用死区和平滑处理
- 集成S曲线轨迹规划器，生成平滑的速度指令（TwistStamped）
- 发布按键状态（Int32消息）
- 支持轨迹规划器重置功能

**`TwistToPoseNode`**:
- 订阅速度消息进行6DOF积分
- 基于配置的初始位姿进行相对积分计算
- 支持Reset按钮重置到初始位姿
- 发布位姿消息（PoseStamped）和TF变换
- 应用6D位姿限制保护

---

## 🛠️ 安装依赖

```bash
sudo apt install ros-humble-teleop-twist-joy
```

---

## 🔧 构建包

```bash
colcon build --packages-select xbox_teleop_nav --symlink-install
source install/setup.bash
```

---

## 🚀 启动系统

### 一键启动（推荐）
```bash
ros2 launch xbox_teleop_nav teleop_pose.launch.py
```

### 分别启动
```bash
# 启动手柄节点
ros2 run joy joy_node

# 启动遥控节点（带S曲线轨迹规划）
ros2 run xbox_teleop_nav teleop_node --ros-args --params-file config/xbox_teleop_config.yaml

# 启动位姿转换节点（6DOF积分）
ros2 run xbox_teleop_nav twist_to_pose_node --ros-args --params-file config/xbox_teleop_config.yaml
```

---

## 🎮 手柄操作说明

### 6DOF控制映射

| 控制轴 | 手柄操作 | 轴编号 | 功能描述 |
|--------|----------|--------|----------|
| **线速度X** | 左摇杆 上下 | a1 | 前进/后退 |
| **线速度Y** | 左摇杆 左右 | a0 | 左移/右移 |
| **线速度Z** | 右扳机 | a4 | 上升/下降 |
| **角速度X** | 右摇杆 左右 | a2 | 绕X轴旋转 |
| **角速度Y** | - | - | 固定为0 |
| **角速度Z** | 右摇杆 上下 | a3 | 偏航旋转 |

### 微调控制

| 操作 | 手柄操作 | 轴编号 | 功能描述 |
|------|----------|--------|----------|
| **X微调** | 十字键 上下 | a7 | X方向精细调整 |
| **Y微调** | 十字键 左右 | a6 | Y方向精细调整 |

### 按钮功能

| 按钮 | 编号 | 功能 | 描述 |
|------|------|------|------|
| **Start** | b7 | 启动 | 系统启动信号 |
| **Reset** | b6 | 重置 | 重置轨迹规划器和位姿 |
| **Sucker** | b0 | 吸盘 | 吸盘控制信号 |
| **Record** | b3 | 记录 | 记录功能信号 |

---

## ⚙️ 配置参数说明

### 📁 统一配置文件：`config/xbox_teleop_config.yaml`

所有参数集中在一个配置文件中，支持6DOF结构化配置：

```yaml
# Xbox Teleop Navigation 统一配置文件
teleop_node:
  ros__parameters:
    # 发布频率 (Hz) - 轨迹规划器dt = 1/publish_rate
    publish_rate: 100

    # 手柄轴映射 [x, y, z]
    axes:
      linear: [1, 0, 4]      # 线速度轴
      angular: [2, 2, 3]     # 角速度轴
      jog: [7, 6]            # 微调轴

    # 6DOF死区配置
    deadzone:
      linear: [0.05, 0.05, 0.05]
      angular: [0.05, 0.05, 0.05]

    # 6DOF缩放和方向
    scale:
      linear: [1.0, 1.0, 1.0]
      angular: [0.0, 0.0, 1.0]
      jog: [0.05, 0.05]

    direction:
      linear: [1.0, 1.0, 1.0]
      angular: [1.0, 1.0, 1.0]

    # 按钮映射
    buttons:
      start: 7
      reset: 6
      sucker: 0
      record: 3

    # S曲线轨迹规划参数
    trajectory:
      max_velocity: [0.5, 0.5, 0.3, 1.0, 1.0, 1.0]
      max_acceleration: [2.0, 2.0, 1.5, 3.0, 3.0, 3.0]
      max_jerk: [10.0, 10.0, 8.0, 15.0, 15.0, 15.0]
      history_length: 5

twist_to_pose_node:
  ros__parameters:
    # 话题配置
    topics:
      twist_input: "/mofo_teleop/xbox/target_twist"
      pose_output: "/mofo_teleop/target_ee_poses"

    # 坐标系配置
    frames:
      parent: "tool"
      child: "base_link"

    # 6D速度限制
    velocity_limits:
      linear: [0.1, 0.1, 0.1]    # m/s
      angular: [0.1, 0.1, 0.1]   # rad/s

    # 6D位姿限制
    pose_limits:
      position:
        min: [-1.0, -1.0, -1.0]
        max: [1.0, 1.0, 1.0]
      orientation:
        min: [-3.14, -3.14, -3.14]
        max: [3.14, 3.14, 3.14]

    # 6D初始位姿
    initial_pose:
      position: [0.0, 0.0, 0.0]     # m
      orientation: [0.0, 0.0, 0.0]  # degrees
```

---

## 🔄 话题接口

### 📥 订阅话题

| 话题名 | 类型 | 节点 | 描述 |
|--------|------|------|------|
| `/joy` | `sensor_msgs/Joy` | TeleopNode | 手柄输入数据 |
| `/mofo_teleop/xbox/target_twist` | `geometry_msgs/TwistStamped` | TwistToPoseNode | S曲线规划后的速度指令 |
| `/mofo_teleop/reset` | `std_msgs/Int32` | TwistToPoseNode | Reset按钮状态 |

### 📤 发布话题

| 话题名 | 类型 | 节点 | 描述 |
|--------|------|------|------|
| `/mofo_teleop/xbox/target_twist` | `geometry_msgs/TwistStamped` | TeleopNode | S曲线规划后的6DOF速度指令 |
| `/mofo_teleop/target_ee_poses` | `geometry_msgs/PoseStamped` | TwistToPoseNode | 积分后的6DOF位姿指令 |
| `/mofo_teleop/start` | `std_msgs/Int32` | TeleopNode | Start按钮状态 |
| `/mofo_teleop/reset` | `std_msgs/Int32` | TeleopNode | Reset按钮状态 |
| `/mofo_teleop/sucker` | `std_msgs/Int32` | TeleopNode | Sucker按钮状态 |
| `/mofo_teleop/record` | `std_msgs/Int32` | TeleopNode | Record按钮状态 |

### 🔗 TF变换

| 变换 | 节点 | 描述 |
|------|------|------|
| `tool` → `base_link` | TwistToPoseNode | 6DOF位姿变换广播 |



## � 核心技术特性

### S曲线轨迹规划
- **平滑控制**: 避免突然的速度变化，提供丝滑的操作体验
- **三级限制**: 速度、加速度、jerk三级限制确保安全
- **向量化计算**: 高效的NumPy向量化处理，6DOF同时规划
- **自适应dt**: 轨迹规划时间步长自动适配发布频率 (dt = 1/freq)

### 6DOF统一管理
- **位置+姿态**: 3D位置和3D姿态统一处理
- **向量化限制**: 6D速度限制和位姿限制向量化处理
- **相对积分**: 支持动态重置起点的相对位置积分
- **实时TF**: 实时发布6DOF坐标变换

### 高效代码设计
- **向量化死区**: 一次性处理所有轴的死区和平滑
- **批量消息**: 统一创建和发布多个话题消息
- **内存优化**: 减少临时对象创建，复用numpy数组
- **配置驱动**: 单一配置文件管理所有参数

## 📁 项目结构

```
xbox_teleop_nav/
├── config/
│   └── xbox_teleop_config.yaml        # 统一配置文件
├── launch/
│   └── teleop_pose.launch.py          # 启动文件
├── xbox_teleop_nav/
│   ├── teleop_node.py                 # 遥控节点(S曲线轨迹规划)
│   └── twist_to_pose_node.py          # 位姿转换节点(6DOF积分)
├── OPTIMIZATION_SUMMARY.md            # 优化总结文档
├── setup.py
├── package.xml
└── README.md
```

## 🎯 性能优势

- **代码简洁**: 相比原版减少30%+代码行数
- **执行效率**: 向量化计算提升处理速度
- **配置统一**: 单文件管理所有参数，易于维护
- **功能完整**: 保持所有原有功能，增加新特性
- **实时性强**: 支持高频率控制(最高250Hz+)

## 💡 使用示例

### 监控话题数据
```bash
# 监控速度指令
ros2 topic echo /mofo_teleop/xbox/target_twist

# 监控位姿输出
ros2 topic echo /mofo_teleop/target_ee_poses

# 监控按钮状态
ros2 topic echo /mofo_teleop/reset
```

### 自定义配置
```bash
# 复制配置文件进行自定义
cp config/xbox_teleop_config.yaml my_custom_config.yaml

# 使用自定义配置启动
ros2 run xbox_teleop_nav teleop_node --ros-args --params-file my_custom_config.yaml
```

### 调试模式
```bash
# 启动时显示详细日志
ros2 launch xbox_teleop_nav teleop_pose.launch.py --ros-args --log-level DEBUG
```

## 🔧 故障排除

### 1. 手柄无响应
```bash
# 检查手柄设备
ls /dev/input/js*

# 测试手柄输入
ros2 topic echo /joy

# 检查joy_node是否运行
ros2 node list | grep joy
```

### 2. 没有速度输出
```bash
# 检查话题连接
ros2 topic info /mofo_teleop/xbox/target_twist

# 检查节点状态
ros2 node list | grep teleop
```

### 3. 位姿积分异常
```bash
# 检查初始位姿设置
ros2 param get /twist_to_pose_node initial_pose.position

# 重置位姿
ros2 topic pub --once /mofo_teleop/reset std_msgs/msg/Int32 "data: 1"
```

### 4. 性能优化
```yaml
# 在配置文件中调整频率
publish_rate: 250  # 提高到250Hz获得更好响应

# 调整轨迹规划参数
trajectory:
  max_acceleration: [3.0, 3.0, 2.0, 4.0, 4.0, 4.0]  # 提高加速度
```

## 📚 更多信息

- 详细优化说明：查看 `OPTIMIZATION_SUMMARY.md`
- 配置参数详解：查看 `config/xbox_teleop_config.yaml` 注释
- 技术实现细节：查看源码注释

---

**开发团队**: 基于joy实现逻辑优化，集成S曲线轨迹规划和6DOF统一控制
**版本**: 优化版 v2.0
**更新**: 2024年 - 向量化计算、统一配置、性能优化
